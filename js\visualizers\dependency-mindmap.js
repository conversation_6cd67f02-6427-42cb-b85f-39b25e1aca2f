/**
 * Dependency Mind Map Visualizer
 * Visualizes dependencies as a mind map using D3.js
 */

import BaseVisualizer from './base-visualizer.js';
import DOMUtils from '../utils/dom-utils.js';

class DependencyMindMap extends BaseVisualizer {
    constructor(options = {}) {
        super(options);
        
        // Additional configuration
        this.config = {
            ...this.config,
            nodeRadius: options.nodeRadius || 6,
            nodeSpacing: options.nodeSpacing || 150,
            levelSpacing: options.levelSpacing || 180,
            transitionDuration: options.transitionDuration || 750,
            colorScheme: options.colorScheme || {
                js: '#f1e05a',    // JavaScript - yellow
                jsx: '#f1e05a',   // JSX - yellow
                ts: '#3178c6',    // TypeScript - blue
                tsx: '#3178c6',   // TSX - blue
                css: '#563d7c',   // CSS - purple
                scss: '#c6538c',  // SCSS - pink
                html: '#e34c26',  // HTML - orange
                json: '#292929',  // JSON - dark gray
                md: '#083fa1',    // Markdown - navy blue
                default: '#7e7e7e' // Default - gray
            }
        };
        
        // D3 elements
        this.svg = null;
        this.zoom = null;
        this.container = null;
        this.tooltip = null;
        
        // Tree layout
        this.treeLayout = null;
        this.root = null;
        this.nodes = [];
        this.links = [];
        
        // State
        this.selectedNode = null;
        this.hoveredNode = null;
        this.transform = { x: 0, y: 0, k: 1 };
        this.nodeMap = new Map(); // Map of node id to node data
        
        // Bind additional methods
        this.handleNodeClick = this.handleNodeClick.bind(this);
        this.handleNodeMouseOver = this.handleNodeMouseOver.bind(this);
        this.handleNodeMouseOut = this.handleNodeMouseOut.bind(this);
        this.handleZoom = this.handleZoom.bind(this);
        this.handleToggleClick = this.handleToggleClick.bind(this);
        this.centerNode = this.centerNode.bind(this);
        this.toggleChildren = this.toggleChildren.bind(this);
    }

    /**
     * Initialize the mind map visualization
     * @returns {DependencyMindMap} - This instance for chaining
     */
    initialize() {
        if (this.isInitialized) return this;
        
        // Call parent initialize
        super.initialize();
        
        // Create SVG container using D3
        this.svg = d3.create('svg')
            .attr('width', this.config.width)
            .attr('height', this.config.height)
            .attr('class', 'dependency-mindmap');
        
        // Create zoom behavior
        this.zoom = d3.zoom()
            .scaleExtent([0.1, 3])
            .on('zoom', this.handleZoom);
        
        // Apply zoom to SVG
        this.svg.call(this.zoom);
        
        // Create container for graph elements
        this.svgContainer = this.svg.append('g')
            .attr('class', 'mindmap-container');
        
        // Create tooltip
        this.tooltip = DOMUtils.createElement('div', {
            class: 'node-tooltip'
        });
        
        // Add SVG to DOM
        this.container.appendChild(this.svg.node());
        this.container.appendChild(this.tooltip);
        
        // Create tree layout
        this.treeLayout = d3.tree()
            .nodeSize([this.config.nodeSpacing, this.config.levelSpacing])
            .separation((a, b) => {
                return a.parent === b.parent ? 1 : 1.2;
            });
        
        return this;
    }

    /**
     * Set data for the mind map
     * @param {Object} dataModel - Data model instance
     * @returns {DependencyMindMap} - This instance for chaining
     */
    setDataModel(dataModel) {
        super.setDataModel(dataModel);
        
        if (dataModel) {
            // Clear node map
            this.nodeMap.clear();
            
            // Get nodes and links from data model
            const nodes = dataModel.getNodes();
            const links = dataModel.getLinks();
            
            // Create node map for quick lookups
            nodes.forEach(node => {
                this.nodeMap.set(node.id, {
                    ...node,
                    _children: [], // Hidden children
                    children: []   // Visible children
                });
            });
            
            // Build tree structure
            links.forEach(link => {
                const source = this.nodeMap.get(link.source);
                const target = this.nodeMap.get(link.target);
                
                if (source && target) {
                    // Add target as child of source
                    source.children.push(target);
                }
            });
            
            // Find root nodes (nodes with no incoming links)
            const rootNodes = Array.from(this.nodeMap.values()).filter(node => {
                return !links.some(link => link.target === node.id);
            });
            
            // Create virtual root if multiple root nodes
            if (rootNodes.length > 1) {
                this.root = {
                    id: 'virtual-root',
                    name: 'Project Root',
                    path: '',
                    type: 'root',
                    children: rootNodes,
                    _children: []
                };
            } else if (rootNodes.length === 1) {
                this.root = rootNodes[0];
            } else if (nodes.length > 0) {
                // No root nodes found, but there are nodes.
                // Fallback to using the first node in the list as the root.
                this.root = this.nodeMap.get(nodes[0].id);
                if (!this.root) { // Should not happen if nodeMap is built correctly from nodes
                    console.warn('Fallback root node (nodes[0]) not found in nodeMap. Setting root to null.');
                    this.root = null;
                }
            } else {
                // No root nodes found AND no nodes available in the data model.
                console.warn('No nodes available in the data model to select a root. Setting root to null.');
                this.root = null;
            }
        }
        
        return this;
    }

    /**
     * Render the mind map visualization
     * @returns {DependencyMindMap} - This instance for chaining
     */
    render() {
        if (!this.isInitialized) this.initialize();
        if (!this.dataModel || !this.root) {
            console.warn('No data available for visualization');
            return this;
        }
        
        this.isRendering = true;
        
        // Show loading indicator
        this.showLoading();
        
        // Apply tree layout
        this.update(this.root);
        
        // Center the root node
        setTimeout(() => {
            this.centerNode(this.root);
            this.hideLoading();
            this.isRendering = false;
        }, 100);
        
        return this;
    }

    /**
     * Update the mind map visualization
     * @param {Object} source - Source node for the update
     */
    update(source) {
        // Apply tree layout to get positions
        const treeData = this.treeLayout(d3.hierarchy(this.root));
        
        // Get nodes and links
        const nodes = treeData.descendants();
        const links = treeData.links();
        
        // Normalize for fixed-depth
        nodes.forEach(d => {
            d.y = d.depth * this.config.levelSpacing;
        });
        
        // ****************** Nodes section ***************************

        // Update the nodes
        const node = this.svgContainer.selectAll('g.node')
            .data(nodes, d => d.data.id);
        
        // Enter any new nodes at the parent's previous position
        const nodeEnter = node.enter().append('g')
            .attr('class', 'node')
            .attr('transform', d => `translate(${source.x0 || source.x}, ${source.y0 || source.y})`)
            .on('click', this.handleNodeClick)
            .on('mouseover', this.handleNodeMouseOver)
            .on('mouseout', this.handleNodeMouseOut);
        
        // Add Circle for the nodes
        nodeEnter.append('circle')
            .attr('class', 'node-circle')
            .attr('r', 1e-6)
            .style('fill', d => this.getNodeColor(d.data.type))
            .style('stroke', '#fff')
            .style('stroke-width', 1.5);
        
        // Add labels for the nodes
        nodeEnter.append('text')
            .attr('dy', '.35em')
            .attr('x', d => d.children || d._children ? -13 : 13)
            .attr('text-anchor', d => d.children || d._children ? 'end' : 'start')
            .text(d => d.data.name)
            .style('fill-opacity', 1e-6);
        
        // Add expand/collapse icon
        nodeEnter.append('text')
            .attr('class', 'node-toggle')
            .attr('dy', 3)
            .attr('x', -25)
            .style('text-anchor', 'middle')
            .style('font-size', '10px')
            .style('font-weight', 'bold')
            .style('fill', '#fff')
            .style('cursor', 'pointer')
            .text(d => d.children ? '−' : (d._children && d._children.length > 0 ? '+' : ''))
            .style('fill-opacity', d => (d.children || (d._children && d._children.length > 0)) ? 1 : 0)
            .on('click', this.handleToggleClick);
        
        // UPDATE
        const nodeUpdate = nodeEnter.merge(node);
        
        // Transition to the proper position for the node
        nodeUpdate.transition()
            .duration(this.config.transitionDuration)
            .attr('transform', d => `translate(${d.x}, ${d.y})`);
        
        // Update the node attributes and style
        nodeUpdate.select('circle.node-circle')
            .attr('r', d => (d.data === this.selectedNode) ? this.config.nodeRadius * 1.3 : this.config.nodeRadius)
            .style('fill', d => this.getNodeColor(d.data.type))
            .style('stroke-width', d => (d.data === this.selectedNode) ? 3 : 1.5)
            .attr('cursor', 'pointer');
        
        // Update toggle icon
        nodeUpdate.select('text.node-toggle')
            .text(d => d.children ? '−' : (d._children && d._children.length > 0 ? '+' : ''))
            .style('fill-opacity', d => (d.children || (d._children && d._children.length > 0)) ? 1 : 0);
        
        // Update the node label
        nodeUpdate.select('text:not(.node-toggle)')
            .attr('x', d => d.children || d._children ? -13 : 13)
            .attr('text-anchor', d => d.children || d._children ? 'end' : 'start')
            .style('fill-opacity', 1);
        
        // Remove any exiting nodes
        const nodeExit = node.exit().transition()
            .duration(this.config.transitionDuration)
            .attr('transform', d => `translate(${source.x}, ${source.y})`)
            .remove();
        
        // On exit reduce the node circles size to 0
        nodeExit.select('circle')
            .attr('r', 1e-6);
        
        // On exit reduce the opacity of text labels
        nodeExit.select('text')
            .style('fill-opacity', 1e-6);
        
        // ****************** links section ***************************

        // Update the links
        const link = this.svgContainer.selectAll('path.link')
            .data(links, d => d.target.data.id);
        
        // Enter any new links at the parent's previous position
        const linkEnter = link.enter().insert('path', 'g')
            .attr('class', 'link')
            .attr('d', d => {
                const o = { x: source.x0 || source.x, y: source.y0 || source.y };
                return this.diagonal(o, o);
            })
            .style('fill', 'none')
            .style('stroke', '#555')
            .style('stroke-width', 1.5)
            .style('stroke-opacity', 0.4);
        
        // UPDATE
        const linkUpdate = linkEnter.merge(link);
        
        // Transition back to the parent element position
        linkUpdate.transition()
            .duration(this.config.transitionDuration)
            .attr('d', d => this.diagonal(d.source, d.target))
            .style('stroke-opacity', d => {
                if (this.selectedNode) {
                    return (d.source.data === this.selectedNode || d.target.data === this.selectedNode) ? 0.8 : 0.2;
                }
                return 0.4;
            })
            .style('stroke-width', d => {
                if (this.selectedNode) {
                    return (d.source.data === this.selectedNode || d.target.data === this.selectedNode) ? 2 : 1.5;
                }
                return 1.5;
            });
        
        // Remove any exiting links
        link.exit().transition()
            .duration(this.config.transitionDuration)
            .attr('d', d => {
                const o = { x: source.x, y: source.y };
                return this.diagonal(o, o);
            })
            .remove();
        
        // Store the old positions for transition
        nodes.forEach(d => {
            d.x0 = d.x;
            d.y0 = d.y;
        });
    }

    /**
     * Creates a curved (diagonal) path from parent to the child nodes
     * @param {Object} s - Source coordinates
     * @param {Object} d - Destination coordinates
     * @returns {string} - SVG path string
     */
    diagonal(s, d) {
        return `M ${s.x} ${s.y}
                C ${s.x} ${(s.y + d.y) / 2},
                  ${d.x} ${(s.y + d.y) / 2},
                  ${d.x} ${d.y}`;
    }

    /**
     * Toggle children visibility
     * @param {Object} d - Node data
     */
    toggleChildren(d) {
        if (d.children) {
            d._children = d.children;
            d.children = null;
        } else {
            d.children = d._children;
            d._children = null;
        }
    }

    /**
     * Update only the visual styles of nodes and links without re-rendering
     */
    updateNodeStyles() {
        // Update node styles
        this.svgContainer.selectAll('g.node circle.node-circle')
            .attr('r', d => (d.data === this.selectedNode) ? this.config.nodeRadius * 1.3 : this.config.nodeRadius)
            .style('stroke-width', d => (d.data === this.selectedNode) ? 3 : 1.5);

        // Update link styles
        this.svgContainer.selectAll('path.link')
            .style('stroke-opacity', d => {
                if (this.selectedNode) {
                    return (d.source.data === this.selectedNode || d.target.data === this.selectedNode) ? 0.8 : 0.2;
                }
                return 0.4;
            })
            .style('stroke-width', d => {
                if (this.selectedNode) {
                    return (d.source.data === this.selectedNode || d.target.data === this.selectedNode) ? 2 : 1.5;
                }
                return 1.5;
            });
    }

    /**
     * Get color for node based on file type
     * @param {string} type - File type
     * @returns {string} - Color code
     */
    getNodeColor(type) {
        return this.config.colorScheme[type] || this.config.colorScheme.default;
    }

    /**
     * Handle toggle click event
     * @param {Event} event - Click event
     * @param {Object} d - Node data
     */
    handleToggleClick(event, d) {
        event.stopPropagation();
        this.toggleChildren(d);
        this.update(d);
    }

    /**
     * Handle node click event
     * @param {Event} event - Click event
     * @param {Object} d - Node data
     */
    handleNodeClick(event, d) {
        event.stopPropagation();

        // Toggle selection
        if (this.selectedNode === d.data) {
            this.selectedNode = null;
            this.dataModel.setSelectedNode(null);
        } else {
            this.selectedNode = d.data;
            this.dataModel.setSelectedNode(d.data.id);
        }

        // Center the node
        this.centerNode(d);

        // Update visual state without causing infinite recursion
        this.updateNodeStyles();

        // Dispatch event
        this.dispatchEvent('nodeSelected', { node: this.selectedNode });
    }

    /**
     * Handle node mouseover event
     * @param {Event} event - Mouseover event
     * @param {Object} d - Node data
     */
    handleNodeMouseOver(event, d) {
        this.hoveredNode = d.data;
        
        // Show tooltip
        this.tooltip.innerHTML = `
            <div class="tooltip-title">${d.data.name}</div>
            <div class="tooltip-content">${d.data.path}</div>
        `;
        
        this.tooltip.style.left = `${event.pageX + 10}px`;
        this.tooltip.style.top = `${event.pageY + 10}px`;
        DOMUtils.toggleClass(this.tooltip, 'visible', true);
    }

    /**
     * Handle node mouseout event
     */
    handleNodeMouseOut() {
        this.hoveredNode = null;
        
        // Hide tooltip
        DOMUtils.toggleClass(this.tooltip, 'visible', false);
    }

    /**
     * Handle zoom event
     * @param {Event} event - Zoom event
     */
    handleZoom(event) {
        this.transform = event.transform;
        this.svgContainer.attr('transform', event.transform);
    }

    /**
     * Center a node in the viewport
     * @param {Object} source - Node to center
     */
    centerNode(source) {
        const scale = this.transform.k;
        const x = -source.x * scale + this.config.width / 2;
        const y = -source.y * scale + this.config.height / 2;
        
        this.svg.transition()
            .duration(this.config.transitionDuration)
            .call(
                this.zoom.transform,
                d3.zoomIdentity.translate(x, y).scale(scale)
            );
    }

    /**
     * Handle resize events
     */
    resize() {
        super.resize();
        
        if (this.svg) {
            this.svg
                .attr('width', this.config.width)
                .attr('height', this.config.height);
        }
    }

    /**
     * Clean up resources
     */
    destroy() {
        super.destroy();
        
        if (this.svg) {
            this.svg.remove();
            this.svg = null;
        }
        
        if (this.tooltip) {
            this.tooltip.remove();
            this.tooltip = null;
        }
        
        this.root = null;
        this.nodeMap.clear();
    }
}

// Export the dependency mind map class
export default DependencyMindMap;
