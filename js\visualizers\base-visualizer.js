/**
 * Base Visualizer Class
 * Abstract base class for all visualizers
 */

import DOMUtils from '../utils/dom-utils.js';

class BaseVisualizer {
    constructor(options = {}) {
        console.log('BaseVisualizer constructor - options:', options);
        console.log('BaseVisualizer constructor - options.container:', options.container);
        console.log('BaseVisualizer constructor - options.container type:', typeof options.container);
        console.log('BaseVisualizer constructor - options.container instanceof HTMLElement:', options.container instanceof HTMLElement);

        // Configuration
        this.config = {
            ...options,
            width: options.width || window.innerWidth,
            height: options.height || window.innerHeight,
            theme: options.theme || 'dark',
            container: options.container || 'visualization'
        };

        console.log('BaseVisualizer constructor - this.config.container:', this.config.container);

        // State - Simplified container assignment
        console.log('BaseVisualizer constructor - About to assign this.container');
        console.log('  - this.config.container:', this.config.container);

        // Simplified logic: if it's a string, get by ID, otherwise use it directly
        if (typeof this.config.container === 'string') {
            console.log('  - Container is string, getting by ID');
            this.container = DOMUtils.getById(this.config.container);
        } else {
            console.log('  - Container is not string, using directly');
            this.container = this.config.container;
        }

        console.log('BaseVisualizer constructor - Final this.container:', this.container);

        this.dataModel = null;
        this.isInitialized = false;
        this.isRendering = false;

        // Bind methods
        this.initialize = this.initialize.bind(this);
        this.render = this.render.bind(this);
        this.update = this.update.bind(this);
        this.resize = this.resize.bind(this);
        this.destroy = this.destroy.bind(this);

        // Note: Container validation moved to initialize() method for better error handling
    }

    /**
     * Initialize the visualizer
     * @returns {BaseVisualizer} - This instance for chaining
     */
    initialize() {
        if (this.isInitialized) return this;

        console.log('BaseVisualizer.initialize - this.container:', this.container);
        console.log('BaseVisualizer.initialize - this.config.container:', this.config.container);

        // Check if container exists and is a valid HTMLElement
        const isValidContainer = this.container &&
            typeof this.container === 'object' &&
            this.container.nodeType === 1 &&
            typeof this.container.appendChild === 'function';

        if (!isValidContainer) {
            console.log('BaseVisualizer.initialize - Container validation failed');
            console.log('  - this.container:', this.container);
            console.log('  - instanceof HTMLElement:', this.container instanceof HTMLElement);
            console.log('  - nodeType:', this.container?.nodeType);
            console.log('  - appendChild function:', typeof this.container?.appendChild);

            const containerDesc = typeof this.config.container === 'string'
                ? `"${this.config.container}"`
                : `[${typeof this.config.container}]`;
            throw new Error(`Container element ${containerDesc} not found or is not a valid HTMLElement`);
        }

        // Set up resize handler
        window.addEventListener('resize', this.resize);

        this.isInitialized = true;
        return this;
    }

    /**
     * Set data model
     * @param {Object} dataModel - Data model instance
     * @returns {BaseVisualizer} - This instance for chaining
     */
    setDataModel(dataModel) {
        this.dataModel = dataModel;
        return this;
    }

    /**
     * Render the visualization
     * This method should be overridden by subclasses
     * @returns {BaseVisualizer} - This instance for chaining
     */
    render() {
        if (!this.isInitialized) this.initialize();
        if (!this.dataModel) {
            console.warn('No data model set for visualization');
            return this;
        }
        
        this.isRendering = true;
        
        // Subclasses should implement their own rendering logic
        
        this.isRendering = false;
        return this;
    }

    /**
     * Update the visualization
     * This method should be overridden by subclasses
     * @returns {BaseVisualizer} - This instance for chaining
     */
    update() {
        if (!this.isInitialized || !this.dataModel) return this;
        
        // Subclasses should implement their own update logic
        
        return this;
    }

    /**
     * Handle resize events
     * @param {Event} event - Resize event
     */
    resize(event) {
        this.config.width = this.container.clientWidth || window.innerWidth;
        this.config.height = this.container.clientHeight || window.innerHeight;
        
        // Subclasses should implement their own resize logic
        
        return this;
    }

    /**
     * Clean up resources
     */
    destroy() {
        window.removeEventListener('resize', this.resize);
        
        // Subclasses should implement their own cleanup logic
        
        this.isInitialized = false;
    }

    /**
     * Show loading indicator
     */
    showLoading() {
        const loadingOverlay = DOMUtils.createElement('div', {
            class: 'loading-overlay'
        });
        
        const spinner = DOMUtils.createElement('div', {
            class: 'spinner'
        });
        
        loadingOverlay.appendChild(spinner);
        this.container.appendChild(loadingOverlay);
    }

    /**
     * Hide loading indicator
     */
    hideLoading() {
        const loadingOverlay = this.container.querySelector('.loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.remove();
        }
    }

    /**
     * Dispatch a custom event
     * @param {string} eventName - Name of the event
     * @param {Object} detail - Event detail data
     */
    dispatchEvent(eventName, detail = {}) {
        DOMUtils.dispatchCustomEvent(`visualizer:${eventName}`, detail, this.container);
    }
}

// Export the base visualizer class
export default BaseVisualizer;
