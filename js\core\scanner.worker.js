/**
 * Web Worker for scanning dependencies
 * Handles file parsing and dependency extraction in a separate thread
 */

// Global error handler for the worker
self.onerror = function(message, source, lineno, colno, error) {
    const errorMessage = typeof message === 'string' ? message : (error && error.message ? error.message : 'Unknown worker error');
    self.postMessage({
        type: 'error',
        message: `Worker script error: ${errorMessage}`,
        details: `At ${source}:${lineno}:${colno}. Stack: ${error ? error.stack : 'N/A'}`
    });
    // Important to return true to prevent the browser's default error handling
    // which might terminate the worker silently or with a less informative console message.
    return true; 
};

// Worker state
const state = {
    projectRootPath: '',
    files: [],
    processedFiles: 0,
    totalFiles: 0,
    excludeRules: [],
    results: {
        nodes: [],
        links: [],
        metadata: {}
    }
};

// Regular expressions for dependency detection
const patterns = {
    // JavaScript imports and requires
    jsImport: /import\s+(?:(?:(\*\s+as\s+)?([a-zA-Z0-9_$]+)|\{([^}]+)\})\s+from\s+)?['"]([^'"]+)['"]/g,
    jsRequire: /(?:const|let|var)\s+(?:([a-zA-Z0-9_$]+)|\{([^}]+)\})\s*=\s*require\s*\(\s*['"]([^'"]+)['"]\s*\)/g,
    jsRequireSimple: /require\s*\(\s*['"]([^'"]+)['"]\s*\)/g,
    jsExport: /export\s+(?:(default)\s+)?(?:(function|class|const|let|var)\s+)?([a-zA-Z0-9_$]+)/g,
    jsExportFrom: /export\s+(?:\*|(?:\{([^}]+)\}))\s+from\s+['"]([^'"]+)['"]/g,
    jsExportAll: /export\s+\*\s+from\s+['"]([^'"]+)['"]/g,
    
    // TypeScript specific patterns
    tsImportType: /import\s+type\s+(?:(?:(\*\s+as\s+)?([a-zA-Z0-9_$]+)|\{([^}]+)\})\s+from\s+)?['"]([^'"]+)['"]/g,
    tsExportType: /export\s+type\s+([a-zA-Z0-9_$]+)/g,
    tsInterface: /(?:export\s+)?interface\s+([a-zA-Z0-9_$]+)/g,
    
    // Function and class detection
    function: /(?:export\s+)?(?:async\s+)?function\s+([a-zA-Z0-9_$]+)/g,
    arrowFunction: /(?:export\s+)?(?:const|let|var)\s+([a-zA-Z0-9_$]+)\s*=\s*(?:async\s*)?\([^)]*\)\s*=>/g,
    class: /(?:export\s+)?class\s+([a-zA-Z0-9_$]+)/g,
    
    // CSS imports
    cssImport: /@import\s+(?:url\()?\s*['"]([^'"]+)['"]\s*\)?/g,
    
    // HTML script and link tags
    htmlScript: /<script[^>]*src\s*=\s*['"]([^'"]+)['"][^>]*>/g,
    htmlLink: /<link[^>]*href\s*=\s*['"]([^'"]+)['"][^>]*>/g,
    
    // Event listeners
    eventListener: /addEventListener\s*\(\s*['"]([^'"]+)['"]/g,
    
    // Variable declarations
    varDeclaration: /(?:const|let|var)\s+([a-zA-Z0-9_$]+)\s*=/g
};

/**
 * Normalize a path by converting backslashes to forward slashes
 * @param {string} pathStr - Path to normalize
 * @param {string} rootStr - Optional root path for relative paths
 * @returns {string} - Normalized path
 */
function normalizePath(pathStr, rootStr = '') {
    if (typeof pathStr !== 'string') pathStr = '';
    if (typeof rootStr !== 'string') rootStr = '';
    
    let s = pathStr.replace(/\\/g, '/');
    let r = rootStr.replace(/\\/g, '/');
    
    if (r && r.length > 0 && !r.endsWith('/')) { 
        r += '/'; 
    }
    
    let finalPath = s;
    if (r && s.startsWith('.') && !s.startsWith(r)) { 
        finalPath = r + s; 
    }
    
    const parts = finalPath.split('/');
    const stack = [];
    
    for (const part of parts) {
        if (part === '.' || (part === '' && stack.length > 0 && stack[stack.length -1] !== '')) { 
            continue; 
        }
        
        if (part === '..') {
            if (stack.length > 0 && stack[stack.length - 1] !== '..') { 
                stack.pop(); 
            }
            else if (!r || r.trim() === '') { 
                stack.push('..'); 
            }
        } else { 
            stack.push(part); 
        }
    }
    
    return stack.join('/');
}

/**
 * Check if a path should be excluded based on exclude rules
 * @param {string} path - Path to check
 * @returns {boolean} - True if path should be excluded
 */
function shouldExclude(path) {
    if (!path) return true;
    
    for (const rule of state.excludeRules) {
        if (typeof rule === 'string' && path.includes(rule)) {
            return true;
        } else if (rule instanceof RegExp && rule.test(path)) {
            return true;
        }
    }
    
    return false;
}

/**
 * Resolve a relative import path to an absolute path
 * @param {string} importPath - Import path to resolve
 * @param {string} currentFilePath - Path of the current file
 * @returns {string} - Resolved path
 */
function resolveImportPath(importPath, currentFilePath) {
    // Handle node modules
    if (isNodeModule(importPath)) {
        return importPath;
    }
    
    // Get directory of current file
    const currentDir = currentFilePath.substring(0, currentFilePath.lastIndexOf('/') + 1);
    
    // Resolve relative path
    return normalizePath(importPath, currentDir);
}

/**
 * Check if a path is a node module
 * @param {string} path - Path to check
 * @returns {boolean} - True if path is a node module
 */
function isNodeModule(path) {
    if (!path) return false;
    
    if (!path.startsWith('./') && !path.startsWith('../') && 
        !path.includes(':') && !path.startsWith('/')) {
        const lastSegment = path.split('/').pop();
        if (lastSegment && !lastSegment.includes('.')) {
            return true;
        }
    }
    
    const nodeModulePrefixes = ['@', 'node:', 'npm:'];
    for (const prefix of nodeModulePrefixes) {
        if (path.startsWith(prefix)) {
            return true;
        }
    }
    
    return false;
}

/**
 * Extract file extension from path
 * @param {string} path - File path
 * @returns {string} - File extension (without the dot)
 */
function getFileExtension(path) {
    if (!path) return '';
    
    const dotIndex = path.lastIndexOf('.');
    if (dotIndex === -1) return '';
    
    return path.substring(dotIndex + 1).toLowerCase();
}

/**
 * Parse a JavaScript or TypeScript file to extract dependencies
 * @param {string} content - File content
 * @param {string} filePath - File path
 * @returns {Object} - Extracted dependencies and metadata
 */
function parseJavaScript(content, filePath) {
    const result = {
        imports: [],
        exports: [],
        functions: [],
        classes: [],
        variables: [],
        events: []
    };
    
    // Extract imports
    let match;
    
    // ES6 imports
    while ((match = patterns.jsImport.exec(content)) !== null) {
        const [, starAs, defaultImport, namedImports, importPath] = match;
        
        if (importPath) {
            const resolvedPath = resolveImportPath(importPath, filePath);
            result.imports.push({
                path: resolvedPath,
                isDefault: !!defaultImport,
                isNamespace: !!starAs,
                names: namedImports ? namedImports.split(',').map(name => name.trim()) : []
            });
        }
    }
    
    // CommonJS requires
    while ((match = patterns.jsRequire.exec(content)) !== null) {
        const [, defaultImport, namedImports, importPath] = match;
        
        if (importPath) {
            const resolvedPath = resolveImportPath(importPath, filePath);
            result.imports.push({
                path: resolvedPath,
                isDefault: !!defaultImport,
                isNamespace: false,
                names: namedImports ? namedImports.split(',').map(name => name.trim()) : []
            });
        }
    }
    
    // Simple requires
    while ((match = patterns.jsRequireSimple.exec(content)) !== null) {
        const [, importPath] = match;
        
        if (importPath) {
            const resolvedPath = resolveImportPath(importPath, filePath);
            result.imports.push({
                path: resolvedPath,
                isDefault: false,
                isNamespace: false,
                names: []
            });
        }
    }
    
    // Extract exports
    while ((match = patterns.jsExport.exec(content)) !== null) {
        const [, isDefault, type, name] = match;
        
        if (name) {
            result.exports.push({
                name,
                isDefault: !!isDefault,
                type: type || 'variable'
            });
        }
    }
    
    // Export from
    while ((match = patterns.jsExportFrom.exec(content)) !== null) {
        const [, namedExports, importPath] = match;
        
        if (importPath) {
            const resolvedPath = resolveImportPath(importPath, filePath);
            result.exports.push({
                path: resolvedPath,
                names: namedExports ? namedExports.split(',').map(name => name.trim()) : []
            });
        }
    }
    
    // Export all
    while ((match = patterns.jsExportAll.exec(content)) !== null) {
        const [, importPath] = match;
        
        if (importPath) {
            const resolvedPath = resolveImportPath(importPath, filePath);
            result.exports.push({
                path: resolvedPath,
                isAll: true
            });
        }
    }
    
    // Extract functions
    while ((match = patterns.function.exec(content)) !== null) {
        const [, name] = match;
        
        if (name) {
            result.functions.push({
                name,
                type: 'function'
            });
        }
    }
    
    // Extract arrow functions
    while ((match = patterns.arrowFunction.exec(content)) !== null) {
        const [, name] = match;
        
        if (name) {
            result.functions.push({
                name,
                type: 'arrow'
            });
        }
    }
    
    // Extract classes
    while ((match = patterns.class.exec(content)) !== null) {
        const [, name] = match;
        
        if (name) {
            result.classes.push({
                name,
                type: 'class'
            });
        }
    }
    
    // Extract event listeners
    while ((match = patterns.eventListener.exec(content)) !== null) {
        const [, eventName] = match;
        
        if (eventName) {
            result.events.push({
                name: eventName
            });
        }
    }
    
    // Extract variable declarations
    while ((match = patterns.varDeclaration.exec(content)) !== null) {
        const [, name] = match;
        
        if (name) {
            result.variables.push({
                name
            });
        }
    }
    
    return result;
}

/**
 * Parse a CSS file to extract imports
 * @param {string} content - File content
 * @param {string} filePath - File path
 * @returns {Object} - Extracted dependencies and metadata
 */
function parseCSS(content, filePath) {
    const result = {
        imports: []
    };
    
    // Extract imports
    let match;
    while ((match = patterns.cssImport.exec(content)) !== null) {
        const [, importPath] = match;
        
        if (importPath) {
            const resolvedPath = resolveImportPath(importPath, filePath);
            result.imports.push({
                path: resolvedPath
            });
        }
    }
    
    return result;
}

/**
 * Parse an HTML file to extract script and link tags
 * @param {string} content - File content
 * @param {string} filePath - File path
 * @returns {Object} - Extracted dependencies and metadata
 */
function parseHTML(content, filePath) {
    const result = {
        scripts: [],
        links: []
    };
    
    // Extract script tags
    let match;
    while ((match = patterns.htmlScript.exec(content)) !== null) {
        const [, src] = match;
        
        if (src) {
            const resolvedPath = resolveImportPath(src, filePath);
            result.scripts.push({
                path: resolvedPath
            });
        }
    }
    
    // Extract link tags
    while ((match = patterns.htmlLink.exec(content)) !== null) {
        const [, href] = match;
        
        if (href) {
            const resolvedPath = resolveImportPath(href, filePath);
            result.links.push({
                path: resolvedPath
            });
        }
    }
    
    return result;
}

/**
 * Process a file and extract dependencies
 * @param {Object} file - File object with path and content
 * @returns {Object} - Processed file with dependencies
 */
function processFile(file) {
    if (!file || !file.path || shouldExclude(file.path)) {
        return null;
    }
    
    const extension = getFileExtension(file.path);
    let dependencies = {};
    
    switch (extension) {
        case 'js':
        case 'jsx':
        case 'ts':
        case 'tsx':
            dependencies = parseJavaScript(file.content, file.path);
            break;
            
        case 'css':
        case 'scss':
        case 'sass':
        case 'less':
            dependencies = parseCSS(file.content, file.path);
            break;
            
        case 'html':
        case 'htm':
            dependencies = parseHTML(file.content, file.path);
            break;
            
        default:
            // Unsupported file type
            return {
                path: file.path,
                type: extension,
                dependencies: {}
            };
    }
    
    return {
        path: file.path,
        type: extension,
        dependencies
    };
}

/**
 * Build the dependency graph from processed files
 * @param {Array} processedFiles - Array of processed files
 * @returns {Object} - Dependency graph with nodes and links
 */
function buildDependencyGraph(processedFiles) {
    const nodes = [];
    const links = [];
    const nodeMap = {};
    
    // Create nodes
    processedFiles.forEach((file, index) => {
        if (!file) return;
        
        const node = {
            id: index,
            path: file.path,
            type: file.type,
            name: file.path.split('/').pop(),
            imports: [],
            exports: [],
            functions: file.dependencies.functions || [],
            classes: file.dependencies.classes || [],
            variables: file.dependencies.variables || [],
            events: file.dependencies.events || []
        };
        
        nodes.push(node);
        nodeMap[file.path] = index;
    });
    
    // Create links
    processedFiles.forEach((file, sourceIndex) => {
        if (!file || !file.dependencies) return;
        
        // Process imports
        if (file.dependencies.imports) {
            file.dependencies.imports.forEach(importItem => {
                const targetIndex = nodeMap[importItem.path];
                
                if (targetIndex !== undefined) {
                    links.push({
                        source: sourceIndex,
                        target: targetIndex,
                        type: 'import',
                        names: importItem.names || []
                    });
                    
                    // Add to node's imports
                    nodes[sourceIndex].imports.push({
                        path: importItem.path,
                        names: importItem.names || []
                    });
                }
            });
        }
        
        // Process exports
        if (file.dependencies.exports) {
            file.dependencies.exports.forEach(exportItem => {
                if (exportItem.path) {
                    const targetIndex = nodeMap[exportItem.path];
                    
                    if (targetIndex !== undefined) {
                        links.push({
                            source: sourceIndex,
                            target: targetIndex,
                            type: 'export',
                            names: exportItem.names || []
                        });
                    }
                }
                
                // Add to node's exports
                if (exportItem.name) {
                    nodes[sourceIndex].exports.push({
                        name: exportItem.name,
                        isDefault: exportItem.isDefault || false
                    });
                }
            });
        }
        
        // Process HTML scripts and links
        if (file.dependencies.scripts) {
            file.dependencies.scripts.forEach(script => {
                const targetIndex = nodeMap[script.path];
                
                if (targetIndex !== undefined) {
                    links.push({
                        source: sourceIndex,
                        target: targetIndex,
                        type: 'script'
                    });
                }
            });
        }
        
        if (file.dependencies.links) {
            file.dependencies.links.forEach(link => {
                const targetIndex = nodeMap[link.path];
                
                if (targetIndex !== undefined) {
                    links.push({
                        source: sourceIndex,
                        target: targetIndex,
                        type: 'link'
                    });
                }
            });
        }
    });
    
    return { nodes, links };
}

/**
 * Process all files and build the dependency graph
 */
function processAllFiles() {
    const processedFiles = [];
    
    // Process each file
    for (const file of state.files) {
        const processed = processFile(file);
        if (processed) {
            processedFiles.push(processed);
        }
        
        state.processedFiles++;
        
        // Report progress
        if (state.processedFiles % 10 === 0 || state.processedFiles === state.totalFiles) {
            self.postMessage({
                type: 'processing_progress',
                processed: state.processedFiles,
                total: state.totalFiles,
                phase: 'parsing'
            });
        }
    }
    
    // Build dependency graph
    self.postMessage({
        type: 'processing_progress',
        processed: state.processedFiles,
        total: state.totalFiles,
        phase: 'building graph'
    });
    
    const graph = buildDependencyGraph(processedFiles);
    
    // Add metadata
    graph.metadata = {
        totalFiles: state.totalFiles,
        processedFiles: state.processedFiles,
        timestamp: Date.now()
    };
    
    // Send results back to main thread
    self.postMessage({
        type: 'workerResults',
        data: graph
    });
}

// Handle messages from main thread
self.onmessage = function(event) {
    const data = event.data;
    
    try {
        switch (data.type) {
            case 'init':
                state.projectRootPath = data.projectRootPath || '';
                state.totalFiles = data.totalFilesToExpect || 0;
                state.excludeRules = data.excludeRules || [];
                state.files = [];
                state.processedFiles = 0;
                
                self.postMessage({
                    type: 'debug',
                    message: `Worker initialized with ${state.totalFiles} files to process`
                });
                break;
                
            case 'fileChunk':
                if (Array.isArray(data.files)) {
                    state.files.push(...data.files);
                    
                    self.postMessage({
                        type: 'debug',
                        message: `Received ${data.files.length} files, total now ${state.files.length}`
                    });
                }
                break;
                
            case 'processAll':
                processAllFiles();
                break;
                
            default:
                self.postMessage({
                    type: 'error',
                    message: `Unknown message type: ${data.type}`
                });
        }
    } catch (error) {
        self.postMessage({
            type: 'error',
            message: error.message,
            stack: error.stack
        });
    }
};
