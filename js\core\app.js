/**
 * Main Application Module
 * Initializes and manages the dependency visualizer application
 */

import DependencyScanner from './scanner.js';
import DependencyDataModel from './data-model.js';
import DependencyGraph from '../visualizers/dependency-graph.js';
import DependencyMindMap from '../visualizers/dependency-mindmap.js';
import DOMUtils from '../utils/dom-utils.js';

class DependencyVisualizerApp {
    constructor(options = {}) {
        // Configuration
        this.config = {
            container: options.container || 'visualization',
            detailsPanel: options.detailsPanel || 'details-panel-container',
            defaultVisualizer: options.defaultVisualizer || 'mindmap',
            scannerWorkerPath: options.scannerWorkerPath || 'js/core/scanner.worker.js', // Path relative to index.html
            ...options
        };

        // Components
        this.scanner = null;
        this.dataModel = null;
        this.currentVisualizer = null;
        this.visualizers = {};
        
        // DOM elements
        this.container = null;
        this.detailsPanel = null;
        
        // State
        this.isInitialized = false;
        this.isScanning = false;
        
        // Bind methods
        this.initialize = this.initialize.bind(this);
        this.handleFileUpload = this.handleFileUpload.bind(this);
        this.handleNodeSelection = this.handleNodeSelection.bind(this);
        this.switchVisualizer = this.switchVisualizer.bind(this);
        this.showDetailsPanel = this.showDetailsPanel.bind(this);
        this.hideDetailsPanel = this.hideDetailsPanel.bind(this);
    }

    /**
     * Initialize the application
     * @returns {DependencyVisualizerApp} - This instance for chaining
     */
    initialize() {
        if (this.isInitialized) return this;
        
        // Get DOM elements
        this.container = typeof this.config.container === 'string' 
            ? DOMUtils.getById(this.config.container) 
            : this.config.container;
            
        this.detailsPanel = typeof this.config.detailsPanel === 'string' 
            ? DOMUtils.getById(this.config.detailsPanel) 
            : this.config.detailsPanel;
        
        // Initialize components
        this.scanner = new DependencyScanner({
            workerPath: this.config.scannerWorkerPath
        });
        
        this.dataModel = new DependencyDataModel();
        
        // Create container elements programmatically
        if (this.container) {
            // Create container elements for each visualizer using native createElement
            this.graphContainer = document.createElement('div');
            this.graphContainer.id = 'graph-container';
            this.graphContainer.style.width = '100%';
            this.graphContainer.style.height = '100%';
            this.graphContainer.style.display = 'none';

            this.mindmapContainer = document.createElement('div');
            this.mindmapContainer.id = 'mindmap-container';
            this.mindmapContainer.style.width = '100%';
            this.mindmapContainer.style.height = '100%';
            this.mindmapContainer.style.display = 'block';



            // Append containers to the main container
            this.container.appendChild(this.graphContainer);
            this.container.appendChild(this.mindmapContainer);
        } else {
            console.error('Main container is undefined, cannot create visualizer containers');
            // Create fallback containers
            this.graphContainer = document.createElement('div');
            this.graphContainer.id = 'graph-container';
            this.mindmapContainer = document.createElement('div');
            this.mindmapContainer.id = 'mindmap-container';
            document.body.appendChild(this.graphContainer);
            document.body.appendChild(this.mindmapContainer);
        }
        
        // Initialize visualizers with their dedicated containers
        this.visualizers = {
            graph: new DependencyGraph({
                container: this.graphContainer
            }),
            mindmap: new DependencyMindMap({
                container: this.mindmapContainer
            })
        };
        
        // Initialize each visualizer
        Object.values(this.visualizers).forEach(visualizer => {
            try {
                visualizer.initialize();
            } catch (error) {
                console.error(`Failed to initialize visualizer:`, error);
            }
        });
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Switch to default visualizer
        this.switchVisualizer(this.config.defaultVisualizer);
        
        this.isInitialized = true;
        return this;
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Scanner events
        document.addEventListener('scanner:progress', this.handleScanProgress.bind(this));
        document.addEventListener('scanner:complete', this.handleScanComplete.bind(this));
        document.addEventListener('scanner:error', this.handleScanError.bind(this));
        
        // Visualizer events
        document.addEventListener('visualizer:nodeSelected', this.handleNodeSelection);
        
        // UI events
        const fileInput = DOMUtils.getById('file-input');
        if (fileInput) {
            fileInput.addEventListener('change', this.handleFileUpload);
        }
        
        const switchButtons = document.querySelectorAll('.visualizer-switch');
        switchButtons.forEach(button => {
            button.addEventListener('click', (event) => {
                const visualizer = event.target.dataset.visualizer;
                if (visualizer) {
                    this.switchVisualizer(visualizer);
                }
            });
        });
        
        const closeDetailsButton = document.querySelector('.close-button');
        if (closeDetailsButton) {
            closeDetailsButton.addEventListener('click', this.hideDetailsPanel);
        }
        
        // Handle window resize
        window.addEventListener('resize', this.handleResize.bind(this));
    }

    /**
     * Handle file upload event
     * @param {Event} event - File input change event
     */
    async handleFileUpload(event) {
        const files = event.target.files;
        if (!files || files.length === 0) return;
        
        this.isScanning = true;
        
        // Show loading indicator
        this.showLoadingIndicator('Scanning files...');
        
        try {
            // Read files
            const filePromises = Array.from(files).map(file => {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => {
                        resolve({
                            path: file.webkitRelativePath || file.name,
                            content: reader.result
                        });
                    };
                    reader.onerror = reject;
                    reader.readAsText(file);
                });
            });
            
            const fileContents = await Promise.all(filePromises);
            
            // Start scanning
            const rootPath = '';
            const results = await this.scanner.startScan(fileContents, rootPath);
            
            // Process results
            this.dataModel.loadData(results);
            
            // Update visualization
            this.currentVisualizer.setDataModel(this.dataModel);
            this.currentVisualizer.render();
            
        } catch (error) {
            console.error('Error scanning files:', error);
            this.showError(`Error scanning files: ${error.message}`);
        } finally {
            this.isScanning = false;
            this.hideLoadingIndicator();
        }
    }

    /**
     * Handle scan progress event
     * @param {CustomEvent} event - Progress event
     */
    handleScanProgress(event) {
        const { processed, total, percentage, phase } = event.detail;
        console.log(`Scan progress: ${phase} - ${processed}/${total} (${percentage}%)`);
    }
    
    /**
     * Handle scan complete event
     * @param {Event} event - Scan complete event
     */
    handleScanComplete(event) {
        this.isScanning = false;
        
        if (event.detail && event.detail.data) {
            this.dataModel.loadData(event.detail.data);
            
            if (this.currentVisualizer) {
                this.currentVisualizer.setDataModel(this.dataModel);
                this.currentVisualizer.render();
            }
            
            // Dispatch event
            DOMUtils.dispatchCustomEvent('app:scanComplete', {
                data: event.detail.data
            });
        }
    }
    
    /**
     * Process scan results from the worker
     * @param {Object} scanResults - The scan results from the worker
     */
    processScanResults(scanResults) {
        // console.log('Processing scan results from worker'); // Original console log
        // console.log('this.dataModel in processScanResults:', this.dataModel);
        // console.log('Is this.dataModel.loadData a function?', typeof this.dataModel?.loadData === 'function');
        
        if (scanResults) {
            // The scanResults object from main.js has nodes and links (as edges) nested under a 'graph' property.
            // DataModel expects a flat structure: { nodes: [], links: [], metadata: {} }
            const dataForModel = {
                nodes: scanResults.graph?.nodes || [],
                links: scanResults.graph?.edges || [], // main.js stores worker's 'links' as 'graph.edges'
                metadata: scanResults.metadata || scanResults.graph?.metadata || {} // Include metadata if available
            };

            // Ensure dataModel is initialized and has loadData
            if (this.dataModel && typeof this.dataModel.loadData === 'function') {
                this.dataModel.loadData(dataForModel);
            } else {
                console.error('DataModel is not properly initialized or loadData method is missing in processScanResults.', this.dataModel);
                this.showError('Critical error: Data model issue during processing.');
                return;
            }
            
            if (this.currentVisualizer) {
                this.currentVisualizer.setDataModel(this.dataModel);
                this.currentVisualizer.render();
            }
            
            // Dispatch event
            DOMUtils.dispatchCustomEvent('app:scanComplete', {
                data: scanResults
            });
        }
    }

    /**
     * Handle scan error event
     * @param {CustomEvent} event - Error event
     */
    handleScanError(event) {
        const { message } = event.detail;
        console.error('Scan error:', message);
        this.showError(`Scan error: ${message}`);
        this.hideLoadingIndicator();
    }

    /**
     * Handle node selection event
     * @param {CustomEvent} event - Node selection event
     */
    handleNodeSelection(event) {
        const { node } = event.detail;
            
        if (node) {
            this.showNodeDetails(node);
        } else {
            this.hideDetailsPanel();
        }
    }

    /**
     * Handle window resize event
     */
    handleResize() {
        if (this.currentVisualizer) {
            this.currentVisualizer.resize();
        }
    }

    /**
     * Switch to a different visualizer
     * @param {string} visualizerName - Name of the visualizer to switch to
     */
    switchVisualizer(visualizerName) {
        if (!this.visualizers[visualizerName]) {
            console.warn(`Visualizer "${visualizerName}" not found`);
            return;
        }
            
        // Clean up current visualizer
        if (this.currentVisualizer) {
            this.currentVisualizer.destroy();
        }
            
        // Switch to new visualizer
        this.currentVisualizer = this.visualizers[visualizerName];
            
        // Only initialize if not already initialized
        if (!this.currentVisualizer.isInitialized) {
            try {
                this.currentVisualizer.initialize();
            } catch (error) {
                console.error(`Failed to initialize visualizer:`, error);
            }
        }
            
        if (this.dataModel && this.dataModel.getNodes().length > 0) {
            this.currentVisualizer.setDataModel(this.dataModel);
            this.currentVisualizer.render();
        }
        
        // Toggle container visibility
        if (visualizerName === 'graph') {
            this.graphContainer.style.display = 'block';
            this.mindmapContainer.style.display = 'none';
        } else if (visualizerName === 'mindmap') {
            this.graphContainer.style.display = 'none';
            this.mindmapContainer.style.display = 'block';
        }
    }



    /**
     * Show node details in the details panel
     * @param {Object} node - Node data
     */
    showNodeDetails(node) {
        if (!this.detailsPanel) return;
        
        // Update panel title
        const panelTitle = this.detailsPanel.querySelector('.details-panel-header h3');
        if (panelTitle) {
            panelTitle.textContent = node.name;
        }
        
        // Update panel content
        const panelContent = this.detailsPanel.querySelector('.details-panel-content');
        if (panelContent) {
            let html = '';
            
            // File info
            html += `
                <div class="details-section">
                    <h3 class="section-title">File Info</h3>
                    <div class="section-content">
                        <p><strong>Path:</strong> ${node.path}</p>
                        <p><strong>Type:</strong> ${node.type}</p>
                    </div>
                </div>
            `;
            
            // Imports
            if (node.imports && node.imports.length > 0) {
                html += `
                    <div class="details-section">
                        <h3 class="section-title imports-title">Imports (${node.imports.length})</h3>
                        <ul class="section-content">
                            ${node.imports.map(imp => `
                                <li data-path="${imp.path}">
                                    ${imp.path.split('/').pop()}
                                    ${imp.names && imp.names.length > 0 
                                        ? `<span class="import-names">${imp.names.join(', ')}</span>` 
                                        : ''}
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                `;
            }
            
            // Exports
            if (node.exports && node.exports.length > 0) {
                html += `
                    <div class="details-section">
                        <h3 class="section-title exports-title">Exports (${node.exports.length})</h3>
                        <ul class="section-content">
                            ${node.exports.map(exp => `
                                <li>
                                    ${exp.name}
                                    ${exp.isDefault ? '<span class="export-default">(default)</span>' : ''}
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                `;
            }
            
            // Functions
            if (node.functions && node.functions.length > 0) {
                html += `
                    <div class="details-section">
                        <h3 class="section-title functions-title">Functions (${node.functions.length})</h3>
                        <ul class="section-content">
                            ${node.functions.map(func => `
                                <li>${func.name}</li>
                            `).join('')}
                        </ul>
                    </div>
                `;
            }
            
            // Classes
            if (node.classes && node.classes.length > 0) {
                html += `
                    <div class="details-section">
                        <h3 class="section-title classes-title">Classes (${node.classes.length})</h3>
                        <ul class="section-content">
                            ${node.classes.map(cls => `
                                <li>${cls.name}</li>
                            `).join('')}
                        </ul>
                    </div>
                `;
            }
            
            // Events
            if (node.events && node.events.length > 0) {
                html += `
                    <div class="details-section">
                        <h3 class="section-title events-title">Events (${node.events.length})</h3>
                        <ul class="section-content">
                            ${node.events.map(event => `
                                <li>${event.name}</li>
                            `).join('')}
                        </ul>
                    </div>
                `;
            }
            
            panelContent.innerHTML = html;
            
            // Add click handlers for imports
            panelContent.querySelectorAll('.details-section li[data-path]').forEach(item => {
                item.addEventListener('click', () => {
                    const path = item.dataset.path;
                    const node = this.dataModel.getNodeByPath(path);
                    if (node) {
                        this.dataModel.setSelectedNode(node.id);
                        this.currentVisualizer.update();
                        this.showNodeDetails(node);
                    }
                });
            });
        }
        
        // Show panel
        this.showDetailsPanel();
    }

    /**
     * Show the details panel
     */
    showDetailsPanel() {
        if (!this.detailsPanel) return;
        
        DOMUtils.toggleClass(this.detailsPanel, 'open', true);
        DOMUtils.toggleClass(document.querySelector('.content-scroll-area'), 'panel-open', true);
    }

    /**
     * Hide the details panel
     */
    hideDetailsPanel() {
        if (!this.detailsPanel) return;
        
        DOMUtils.toggleClass(this.detailsPanel, 'open', false);
        DOMUtils.toggleClass(document.querySelector('.content-scroll-area'), 'panel-open', false);
    }

    /**
     * Show loading indicator
     * @param {string} message - Loading message
     */
    showLoadingIndicator(message = 'Loading...') {
        let loadingOverlay = document.querySelector('.loading-overlay');
        
        if (!loadingOverlay) {
            loadingOverlay = DOMUtils.createElement('div', {
                class: 'loading-overlay'
            });
            
            const spinner = DOMUtils.createElement('div', {
                class: 'spinner'
            });
            
            const loadingText = DOMUtils.createElement('div', {
                class: 'loading-text',
                style: {
                    color: '#fff',
                    marginTop: '15px',
                    fontSize: '14px'
                }
            }, message);
            
            loadingOverlay.appendChild(spinner);
            loadingOverlay.appendChild(loadingText);
            document.body.appendChild(loadingOverlay);
        } else {
            const loadingText = loadingOverlay.querySelector('.loading-text');
            if (loadingText) {
                loadingText.textContent = message;
            }
        }
    }

    /**
     * Update loading indicator message
     * @param {string} message - New loading message
     */
    updateLoadingIndicator(message) {
        const loadingText = document.querySelector('.loading-overlay .loading-text');
        if (loadingText) {
            loadingText.textContent = message;
        }
    }

    /**
     * Hide loading indicator
     */
    hideLoadingIndicator() {
        const loadingOverlay = document.querySelector('.loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.remove();
        }
    }

    /**
     * Show error message
     * @param {string} message - Error message
     */
    showError(message) {
        const errorContainer = DOMUtils.createElement('div', {
            class: 'error-message',
            style: {
                position: 'fixed',
                top: '20px',
                left: '50%',
                transform: 'translateX(-50%)',
                backgroundColor: 'rgba(244, 67, 54, 0.9)',
                color: '#fff',
                padding: '10px 20px',
                borderRadius: '4px',
                boxShadow: '0 2px 10px rgba(0, 0, 0, 0.2)',
                zIndex: '9999',
                maxWidth: '80%'
            }
        }, message);
        
        document.body.appendChild(errorContainer);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            errorContainer.remove();
        }, 5000);
    }
}

// Export the application class
export default DependencyVisualizerApp;
