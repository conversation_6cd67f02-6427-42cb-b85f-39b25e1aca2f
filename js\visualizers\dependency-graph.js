/**
 * Dependency Graph Visualizer
 * Visualizes dependencies as a force-directed graph using D3.js
 */

import BaseVisualizer from './base-visualizer.js';
import DOMUtils from '../utils/dom-utils.js';

class DependencyGraph extends BaseVisualizer {
    constructor(options = {}) {
        super(options);
        
        // Additional configuration
        this.config = {
            ...this.config,
            nodeRadius: options.nodeRadius || 8,
            linkDistance: options.linkDistance || 100,
            chargeStrength: options.chargeStrength || -300,
            centerForce: options.centerForce || 0.1,
            minimapSize: options.minimapSize || 150,
            colorScheme: options.colorScheme || {
                js: '#f1e05a',    // JavaScript - yellow
                jsx: '#f1e05a',   // JSX - yellow
                ts: '#3178c6',    // TypeScript - blue
                tsx: '#3178c6',   // TSX - blue
                css: '#563d7c',   // CSS - purple
                scss: '#c6538c',  // SCSS - pink
                html: '#e34c26',  // HTML - orange
                json: '#292929',  // JSON - dark gray
                md: '#083fa1',    // Markdown - navy blue
                default: '#7e7e7e' // Default - gray
            }
        };
        
        // D3 elements
        this.svg = null;
        this.simulation = null;
        this.zoom = null;
        this.container = null;
        this.minimap = null;
        this.tooltip = null;
        
        // Graph elements
        this.nodes = [];
        this.links = [];
        this.nodeElements = null;
        this.linkElements = null;
        this.labelElements = null;
        
        // State
        this.selectedNode = null;
        this.hoveredNode = null;
        this.transform = { x: 0, y: 0, k: 1 };
        
        // Bind additional methods
        this.handleNodeClick = this.handleNodeClick.bind(this);
        this.handleNodeMouseOver = this.handleNodeMouseOver.bind(this);
        this.handleNodeMouseOut = this.handleNodeMouseOut.bind(this);
        this.handleZoom = this.handleZoom.bind(this);
        this.updateMinimap = this.updateMinimap.bind(this);
        this.centerGraph = this.centerGraph.bind(this);
        this.fitGraph = this.fitGraph.bind(this);
    }

    /**
     * Initialize the graph visualization
     * @returns {DependencyGraph} - This instance for chaining
     */
    initialize() {
        if (this.isInitialized) return this;
        
        // Call parent initialize
        super.initialize();
        
        this.container = document.getElementById("visualization-container");
        
        // Ensure we have a container element
        if (!this.container) {
            console.warn('Container is undefined, creating a fallback container');
            // Create a fallback container if none exists
            this.container = document.getElementById('visualization-container') || document.body;
        } else if (typeof this.container === 'string') {
            // If container is a string (ID), get the element
            const containerElement = document.getElementById(this.container);
            if (containerElement) {
                this.container = containerElement;
            } else {
                console.warn(`Container element with ID '${this.container}' not found, using fallback`);
                this.container = document.getElementById('visualization-container') || document.body;
            }
        }
        // If container is already a DOM element, use it as-is
        
        // Create SVG container
        this.svg = d3.create('svg')
            .attr('width', this.config.width)
            .attr('height', this.config.height)
            .attr('class', 'dependency-graph');
        
        // Create zoom behavior
        this.zoom = d3.zoom()
            .scaleExtent([0.1, 8])
            .on('zoom', this.handleZoom);
        
        // Apply zoom to SVG
        this.svg.call(this.zoom);
        
        // Create container for graph elements (D3 selection)
        this.svgContainer = this.svg.append('g')
            .attr('class', 'graph-container');
        
        // Create tooltip
        this.tooltip = DOMUtils.createElement('div', {
            class: 'node-tooltip'
        });
        
        // Create minimap
        this.createMinimap();
        
        // Add SVG to DOM
        this.container.appendChild(this.svg.node());
        this.container.appendChild(this.tooltip);
        
        return this;
    }

    /**
     * Create minimap for navigation
     */
    createMinimap() {
        // Create minimap container
        this.minimap = DOMUtils.createElement('div', {
            class: 'graph-minimap',
            style: {
                width: `${this.config.minimapSize}px`,
                height: `${this.config.minimapSize}px`
            }
        });
        
        // Create minimap SVG
        this.minimapSvg = d3.create('svg')
            .attr('width', this.config.minimapSize)
            .attr('height', this.config.minimapSize)
            .attr('class', 'minimap-svg');
        
        // Create minimap viewport indicator
        this.minimapViewport = DOMUtils.createElement('div', {
            class: 'minimap-viewport'
        });
        
        // Add elements to DOM
        this.minimap.appendChild(this.minimapSvg.node());
        this.minimap.appendChild(this.minimapViewport);
        
        // The container element should be available at this point
        // since we've added fallback handling in the initialize method
        this.container.appendChild(this.minimap);
    }

    /**
     * Set data for the graph
     * @param {Object} dataModel - Data model instance
     * @returns {DependencyGraph} - This instance for chaining
     */
    setDataModel(dataModel) {
        super.setDataModel(dataModel);
        
        if (dataModel) {
            this.nodes = dataModel.getNodes().map(node => ({
                ...node,
                radius: this.config.nodeRadius,
                color: this.getNodeColor(node.type)
            }));
            
            this.links = dataModel.getLinks().map(link => ({
                ...link,
                color: this.getLinkColor(link.type)
            }));
        }
        
        return this;
    }

    /**
     * Get color for node based on file type
     * @param {string} type - File type
     * @returns {string} - Color code
     */
    getNodeColor(type) {
        return this.config.colorScheme[type] || this.config.colorScheme.default;
    }

    /**
     * Get color for link based on link type
     * @param {string} type - Link type
     * @returns {string} - Color code
     */
    getLinkColor(type) {
        switch (type) {
            case 'import':
                return 'rgba(255, 255, 255, 0.3)';
            case 'export':
                return 'rgba(76, 175, 80, 0.5)';
            case 'script':
                return 'rgba(255, 193, 7, 0.5)';
            case 'link':
                return 'rgba(33, 150, 243, 0.5)';
            default:
                return 'rgba(255, 255, 255, 0.2)';
        }
    }

    /**
     * Render the graph visualization
     * @returns {DependencyGraph} - This instance for chaining
     */
    render() {
        if (!this.isInitialized) this.initialize();
        if (!this.dataModel || !this.nodes.length) {
            console.warn('No data available for visualization');
            return this;
        }
        
        this.isRendering = true;
        
        // Show loading indicator
        this.showLoading();
        
        // Create force simulation
        this.simulation = d3.forceSimulation(this.nodes)
            .force('link', d3.forceLink(this.links)
                .id(d => d.id)
                .distance(this.config.linkDistance))
            .force('charge', d3.forceManyBody()
                .strength(this.config.chargeStrength))
            .force('center', d3.forceCenter(
                this.config.width / 2, 
                this.config.height / 2)
                .strength(this.config.centerForce))
            .force('collision', d3.forceCollide()
                .radius(d => d.radius * 1.2))
            .on('tick', () => this.updatePositions());
        
        // Create links
        this.linkElements = this.container.selectAll('.link')
            .data(this.links)
            .enter()
            .append('line')
            .attr('class', 'link')
            .attr('stroke', d => d.color)
            .attr('stroke-width', 1)
            .attr('stroke-opacity', 0.6);
        
        // Create nodes
        this.nodeElements = this.container.selectAll('.node')
            .data(this.nodes)
            .enter()
            .append('circle')
            .attr('class', 'node')
            .attr('r', d => d.radius)
            .attr('fill', d => d.color)
            .attr('stroke', '#fff')
            .attr('stroke-width', 1.5)
            .attr('stroke-opacity', 0.8)
            .call(d3.drag()
                .on('start', this.dragStarted.bind(this))
                .on('drag', this.dragged.bind(this))
                .on('end', this.dragEnded.bind(this)))
            .on('click', this.handleNodeClick)
            .on('mouseover', this.handleNodeMouseOver)
            .on('mouseout', this.handleNodeMouseOut);
        
        // Create labels
        this.labelElements = this.container.selectAll('.label')
            .data(this.nodes)
            .enter()
            .append('text')
            .attr('class', 'label')
            .attr('text-anchor', 'middle')
            .attr('dy', '.35em')
            .attr('font-size', '10px')
            .attr('fill', '#fff')
            .text(d => d.name)
            .attr('opacity', 0);
        
        // Render minimap
        this.renderMinimap();
        
        // Center and fit the graph
        setTimeout(() => {
            this.fitGraph();
            this.hideLoading();
            this.isRendering = false;
        }, 500);
        
        return this;
    }

    /**
     * Update node and link positions on simulation tick
     */
    updatePositions() {
        this.linkElements
            .attr('x1', d => d.source.x)
            .attr('y1', d => d.source.y)
            .attr('x2', d => d.target.x)
            .attr('y2', d => d.target.y);
        
        this.nodeElements
            .attr('cx', d => d.x)
            .attr('cy', d => d.y);
        
        this.labelElements
            .attr('x', d => d.x)
            .attr('y', d => d.y);
        
        // Update minimap
        this.updateMinimap();
    }

    /**
     * Handle node click event
     * @param {Event} event - Click event
     * @param {Object} node - Node data
     */
    handleNodeClick(event, node) {
        event.stopPropagation();
        
        // Toggle selection
        if (this.selectedNode === node) {
            this.selectedNode = null;
            this.dataModel.setSelectedNode(null);
        } else {
            this.selectedNode = node;
            this.dataModel.setSelectedNode(node.id);
        }
        
        // Update visual state
        this.updateSelectionState();
        
        // Dispatch event
        this.dispatchEvent('nodeSelected', { node: this.selectedNode });
    }

    /**
     * Handle node mouseover event
     * @param {Event} event - Mouseover event
     * @param {Object} node - Node data
     */
    handleNodeMouseOver(event, node) {
        this.hoveredNode = node;
        
        // Show tooltip
        this.tooltip.innerHTML = `
            <div class="tooltip-title">${node.name}</div>
            <div class="tooltip-content">${node.path}</div>
        `;
        
        this.tooltip.style.left = `${event.pageX + 10}px`;
        this.tooltip.style.top = `${event.pageY + 10}px`;
        DOMUtils.toggleClass(this.tooltip, 'visible', true);
        
        // Highlight connected nodes
        this.updateHoverState();
    }

    /**
     * Handle node mouseout event
     */
    handleNodeMouseOut() {
        this.hoveredNode = null;
        
        // Hide tooltip
        DOMUtils.toggleClass(this.tooltip, 'visible', false);
        
        // Reset highlighting
        this.updateHoverState();
    }

    /**
     * Handle zoom event
     * @param {Event} event - Zoom event
     */
    handleZoom(event) {
        this.transform = event.transform;
        this.svgContainer.attr('transform', event.transform);
        this.updateMinimap();
    }

    /**
     * Update node and link appearance based on selection state
     */
    updateSelectionState() {
        if (!this.nodeElements) return;
        
        this.nodeElements
            .attr('stroke-width', d => (d === this.selectedNode) ? 3 : 1.5)
            .attr('r', d => (d === this.selectedNode) ? d.radius * 1.3 : d.radius);
        
        this.labelElements
            .attr('opacity', d => (d === this.selectedNode) ? 1 : 0);
        
        if (this.selectedNode) {
            // Highlight connected links
            this.linkElements
                .attr('stroke-opacity', d => 
                    (d.source === this.selectedNode || d.target === this.selectedNode) ? 0.8 : 0.2)
                .attr('stroke-width', d => 
                    (d.source === this.selectedNode || d.target === this.selectedNode) ? 2 : 1);
        } else {
            // Reset links
            this.linkElements
                .attr('stroke-opacity', 0.6)
                .attr('stroke-width', 1);
        }
    }

    /**
     * Update node and link appearance based on hover state
     */
    updateHoverState() {
        if (!this.nodeElements || !this.hoveredNode) return;
        
        this.nodeElements
            .attr('opacity', d => {
                if (d === this.hoveredNode) return 1;
                
                // Check if connected to hovered node
                const isConnected = this.links.some(link => 
                    (link.source === this.hoveredNode && link.target === d) || 
                    (link.target === this.hoveredNode && link.source === d)
                );
                
                return isConnected ? 0.8 : 0.3;
            });
        
        this.linkElements
            .attr('stroke-opacity', d => 
                (d.source === this.hoveredNode || d.target === this.hoveredNode) ? 0.8 : 0.1);
    }

    /**
     * Render the minimap
     */
    renderMinimap() {
        // Clear previous content
        this.minimapSvg.selectAll('*').remove();
        
        // Create minimap links
        this.minimapSvg.selectAll('.minimap-link')
            .data(this.links)
            .enter()
            .append('line')
            .attr('class', 'minimap-link')
            .attr('stroke', '#555')
            .attr('stroke-width', 0.5)
            .attr('stroke-opacity', 0.4);
        
        // Create minimap nodes
        this.minimapSvg.selectAll('.minimap-node')
            .data(this.nodes)
            .enter()
            .append('circle')
            .attr('class', 'minimap-node')
            .attr('r', 1.5)
            .attr('fill', d => d.color)
            .attr('stroke', '#fff')
            .attr('stroke-width', 0.3);
        
        // Update minimap
        this.updateMinimap();
    }

    /**
     * Update the minimap
     */
    updateMinimap() {
        if (!this.minimapSvg || !this.nodes.length) return;
        
        // Calculate bounds of the graph
        const bounds = this.getGraphBounds();
        const padding = 20;
        
        // Calculate scale to fit graph in minimap
        const graphWidth = bounds.maxX - bounds.minX + padding * 2;
        const graphHeight = bounds.maxY - bounds.minY + padding * 2;
        const scaleX = this.config.minimapSize / graphWidth;
        const scaleY = this.config.minimapSize / graphHeight;
        const scale = Math.min(scaleX, scaleY);
        
        // Update minimap nodes
        this.minimapSvg.selectAll('.minimap-node')
            .attr('cx', d => (d.x - bounds.minX + padding) * scale)
            .attr('cy', d => (d.y - bounds.minY + padding) * scale);
        
        // Update minimap links
        this.minimapSvg.selectAll('.minimap-link')
            .attr('x1', d => (d.source.x - bounds.minX + padding) * scale)
            .attr('y1', d => (d.source.y - bounds.minY + padding) * scale)
            .attr('x2', d => (d.target.x - bounds.minX + padding) * scale)
            .attr('y2', d => (d.target.y - bounds.minY + padding) * scale);
        
        // Update viewport indicator
        const viewportWidth = this.config.width / this.transform.k;
        const viewportHeight = this.config.height / this.transform.k;
        
        const viewportX = (-this.transform.x / this.transform.k - bounds.minX + padding) * scale;
        const viewportY = (-this.transform.y / this.transform.k - bounds.minY + padding) * scale;
        const viewportW = viewportWidth * scale;
        const viewportH = viewportHeight * scale;
        
        this.minimapViewport.style.left = `${viewportX}px`;
        this.minimapViewport.style.top = `${viewportY}px`;
        this.minimapViewport.style.width = `${viewportW}px`;
        this.minimapViewport.style.height = `${viewportH}px`;
    }

    /**
     * Get the bounds of the graph
     * @returns {Object} - Bounds object with minX, minY, maxX, maxY
     */
    getGraphBounds() {
        if (!this.nodes.length) {
            return { minX: 0, minY: 0, maxX: this.config.width, maxY: this.config.height };
        }
        
        let minX = Infinity;
        let minY = Infinity;
        let maxX = -Infinity;
        let maxY = -Infinity;
        
        this.nodes.forEach(node => {
            minX = Math.min(minX, node.x || 0);
            minY = Math.min(minY, node.y || 0);
            maxX = Math.max(maxX, node.x || 0);
            maxY = Math.max(maxY, node.y || 0);
        });
        
        return { minX, minY, maxX, maxY };
    }

    /**
     * Center the graph
     */
    centerGraph() {
        if (!this.svg || !this.nodes.length) return;
        
        const bounds = this.getGraphBounds();
        const centerX = (bounds.minX + bounds.maxX) / 2;
        const centerY = (bounds.minY + bounds.maxY) / 2;
        
        const transform = d3.zoomIdentity
            .translate(this.config.width / 2 - centerX, this.config.height / 2 - centerY)
            .scale(1);
        
        this.svg.transition()
            .duration(750)
            .call(this.zoom.transform, transform);
    }

    /**
     * Fit the graph to the viewport
     */
    fitGraph() {
        if (!this.svg || !this.nodes.length) return;
        
        const bounds = this.getGraphBounds();
        const padding = 40;
        
        const graphWidth = bounds.maxX - bounds.minX + padding * 2;
        const graphHeight = bounds.maxY - bounds.minY + padding * 2;
        
        const scaleX = this.config.width / graphWidth;
        const scaleY = this.config.height / graphHeight;
        const scale = Math.min(scaleX, scaleY, 1); // Cap at 1 to avoid excessive zoom
        
        const centerX = (bounds.minX + bounds.maxX) / 2;
        const centerY = (bounds.minY + bounds.maxY) / 2;
        
        const transform = d3.zoomIdentity
            .translate(this.config.width / 2 - centerX * scale, this.config.height / 2 - centerY * scale)
            .scale(scale);
        
        this.svg.transition()
            .duration(750)
            .call(this.zoom.transform, transform);
    }

    /**
     * Handle drag start event
     * @param {Event} event - Drag event
     */
    dragStarted(event) {
        if (!event.active) this.simulation.alphaTarget(0.3).restart();
        event.subject.fx = event.subject.x;
        event.subject.fy = event.subject.y;
    }

    /**
     * Handle drag event
     * @param {Event} event - Drag event
     */
    dragged(event) {
        event.subject.fx = event.x;
        event.subject.fy = event.y;
    }

    /**
     * Handle drag end event
     * @param {Event} event - Drag event
     */
    dragEnded(event) {
        if (!event.active) this.simulation.alphaTarget(0);
        event.subject.fx = null;
        event.subject.fy = null;
    }

    /**
     * Handle resize events
     */
    resize() {
        super.resize();
        
        if (this.svg) {
            this.svg
                .attr('width', this.config.width)
                .attr('height', this.config.height);
            
            // Update simulation center force
            if (this.simulation) {
                this.simulation.force('center')
                    .x(this.config.width / 2)
                    .y(this.config.height / 2);
                
                this.simulation.alpha(0.3).restart();
            }
        }
    }

    /**
     * Clean up resources
     */
    destroy() {
        super.destroy();
        
        if (this.simulation) {
            this.simulation.stop();
            this.simulation = null;
        }
        
        if (this.svg) {
            this.svg.remove();
            this.svg = null;
        }
        
        if (this.minimap) {
            this.minimap.remove();
            this.minimap = null;
        }
        
        if (this.tooltip) {
            this.tooltip.remove();
            this.tooltip = null;
        }
        
        this.nodes = [];
        this.links = [];
        this.nodeElements = null;
        this.linkElements = null;
        this.labelElements = null;
    }
}

// Export the dependency graph class
export default DependencyGraph;
