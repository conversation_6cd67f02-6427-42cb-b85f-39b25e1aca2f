/* Base styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html, body {
    font-family: var(--font-family-default);
    font-size: var(--font-size-md);
    line-height: 1.5;
    background-color: var(--google-background);
    color: var(--google-on-surface);
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    display: flex;
    flex-direction: column;
}

/* Custom scrollbar for Google-style */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--google-surface);
}

::-webkit-scrollbar-thumb {
    background: var(--google-border);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--google-on-surface-medium);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .visualization-controls {
        top: var(--spacing-sm);
        right: var(--spacing-sm);
        flex-direction: column;
    }
    
    #graph-minimap {
        bottom: var(--spacing-sm);
        right: var(--spacing-sm);
    }
}
